[package]
name = "colony-utils"
version = "0.2.6"
edition = "2024"
authors = ["<PERSON>lish"]
description = "A collection of utilities for interacting with the colonylib metadata framework for the Autonomi decentralized network"
license = "GPL-3.0-only"
repository = "https://github.com/zettawatt/colony-utils"
keywords = ["autonomi", "metadata", "decentralized", "cli", "daemon"]
categories = ["command-line-utilities", "web-programming::http-server"]

[[bin]]
name = "colonyd"
path = "src/bin/colonyd.rs"

[[bin]]
name = "colony"
path = "src/bin/colony.rs"

[[bin]]
name = "ia_downloader"
path = "src/bin/ia_downloader.rs"

[[bin]]
name = "colony_uploader"
path = "src/bin/colony_uploader.rs"

[dependencies]
# Shared dependencies
clap = "4.5.39"
indicatif = "0.17.11"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = { version = "1.45.1", features = ["full"] }
dialoguer = "0.11.0"
dirs = "6.0.0"
uuid = { version = "1.11.0", features = ["v4", "serde"] }

# Daemon-specific dependencies
autonomi = "0.5.0"
axum = "0.8.4"
axum-extra = { version = "0.9.4", features = ["typed-header"] }
bip39 = { version = "2.1.0", features = ["rand"] }
chrono = { version = "0.4.38", features = ["serde"] }
colonylib = "0.5.7"
jsonwebtoken = "9.3.1"
tower = "0.5.1"
tower-http = { version = "0.6.2", features = ["trace", "cors"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }

# CLI-specific dependencies
reqwest = { version = "0.12", features = ["json", "rustls-tls", "multipart", "stream"], default-features = false }
colored = "2.0"
anyhow = "1.0"

# Additional dependencies for ia_downloader
quick-xml = "0.36"
sha2 = "0.10"
hex = "0.4"
url = "2.5"
futures-util = "0.3"
urlencoding = "2.1"
base64 = "0.22"
bytes = "1.0"
ruint = "1.15.0"

# Additional dependencies for colony_uploader

[dev-dependencies]
hyper = { version = "1.6.0", features = ["full"] }
tempfile = "3.14.0"
tokio-test = "0.4.4"
tower = { version = "0.5.1", features = ["util"] }

[profile.dev]
opt-level = 0
incremental = true
